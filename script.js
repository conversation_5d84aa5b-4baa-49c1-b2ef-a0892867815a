// Optimized Resume Application
document.addEventListener('DOMContentLoaded', function() {
    // Print functionality
    function setupPrintButton() {
        if (!document.querySelector('.print-btn')) {
            const printBtn = document.createElement('button');
            printBtn.className = 'print-btn';
            printBtn.innerHTML = '<i class="fas fa-print"></i> Print';
            printBtn.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1e40af;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 8px;
            `;

            printBtn.addEventListener('click', () => window.print());
            document.body.appendChild(printBtn);
        }
    }

    // Initialize print button
    setupPrintButton();

    // Download as PDF functionality
    function setupDownloadButton() {
        if (!document.querySelector('.download-btn')) {
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'download-btn';
            downloadBtn.innerHTML = '<i class="fas fa-download"></i> PDF';
            downloadBtn.style.cssText = `
                position: fixed;
                top: 70px;
                right: 20px;
                background: #059669;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 8px;
            `;

            downloadBtn.addEventListener('click', () => {
                alert('To save as PDF: Press Ctrl+P (Cmd+P), select "Save as PDF", ensure margins are "None" and background graphics are enabled, then click Save.');
            });

            document.body.appendChild(downloadBtn);
        }
    }

    // Initialize download button
    setupDownloadButton();

    // Hide buttons when printing
    window.addEventListener('beforeprint', () => {
        document.querySelectorAll('.print-btn, .download-btn').forEach(btn => btn.style.display = 'none');
    });

    window.addEventListener('afterprint', () => {
        document.querySelectorAll('.print-btn, .download-btn').forEach(btn => btn.style.display = 'flex');
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            e.preventDefault();
            window.print();
        }
    });
});
