/* Optimized Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Source Sans Pro', sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 50%, #e2e8f0 100%);
    padding: 32px 0;
    font-size: 14px;
}

body::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 20% 80%, rgba(252, 129, 129, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(45, 55, 72, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(252, 129, 129, 0.02) 0%, transparent 50%);
    z-index: -1;
}

/* Resume Container */
.resume-container {
    max-width: 8.5in;
    margin: 0 auto;
    background: white;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(252, 129, 129, 0.1);
    border-radius: 16px;
    overflow: hidden;
    position: relative;
}

.resume-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #fc8181, #f56565, #e53e3e);
    z-index: 1;
}

/* Page Layout */
.page {
    width: 8.5in;
    min-height: 11in;
    background: white;
    page-break-after: always;
    page-break-inside: avoid;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.page:last-child {
    page-break-after: avoid;
}

/* Header Styles */
.header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 20px;
    padding: 24px 32px 20px 32px;
    background: linear-gradient(135deg, #2d3748, #4a5568, #1a202c);
    color: white;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(252, 129, 129, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #fc8181, #f56565, #e53e3e);
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    width: 100%;
}

.header-info {
    text-align: center;
}

.name {
    font-family: 'Poppins', sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: white;
    margin-bottom: 6px;
    letter-spacing: -0.5px;
    position: relative;
    z-index: 2;
}

.title {
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 500;
    color: #fed7aa;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    position: relative;
    z-index: 2;
    margin-bottom: 16px;
}

.contact-info {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    position: relative;
    z-index: 2;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: white;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(252, 129, 129, 0.2);
}

.contact-item i {
    color: #fc8181;
    font-size: 14px;
    background: rgba(252, 129, 129, 0.2);
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Section Styles */
.section {
    margin-bottom: 20px;
    page-break-inside: avoid;
    padding: 0 32px;
}

.section-title {
    font-family: 'Poppins', sans-serif;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
    position: relative;
}

.section-title::before {
    content: '';
    position: absolute;
    left: -32px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(180deg, #fc8181, #f56565);
    border-radius: 2px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #fc8181, #f56565);
}

.section-title i {
    color: #fc8181;
    font-size: 16px;
    background: rgba(252, 129, 129, 0.1);
    padding: 8px;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Professional Summary */
.summary-text {
    font-size: 14px;
    line-height: 1.6;
    color: #4a5568;
    text-align: justify;
    background: linear-gradient(135deg, #fef5f5, #fed7d7);
    padding: 20px;
    border-radius: 12px;
    border-left: 4px solid #fc8181;
    position: relative;
    overflow: hidden;
}

.summary-text::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(252, 129, 129, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 16px;
    padding: 16px;
    border-left: 4px solid #d1d5db;
    background: linear-gradient(135deg, #ffffff, #f9fafb);
    border-radius: 8px;
    page-break-inside: avoid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.experience-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #fc8181, transparent);
}

.experience-header {
    margin-bottom: 10px;
}

.job-title {
    font-family: 'Poppins', sans-serif;
    font-size: 15px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.company {
    font-size: 13px;
    color: #c53030;
    font-weight: 600;
    display: block;
    margin-bottom: 3px;
}

.duration {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    font-style: italic;
}

.achievements {
    list-style: none;
    padding-left: 0;
    margin-top: 8px;
}

.achievements li {
    font-size: 13px;
    line-height: 1.5;
    color: #374151;
    margin-bottom: 6px;
    padding-left: 20px;
    position: relative;
}

.achievements li::before {
    content: "▸";
    color: #fc8181;
    font-weight: 700;
    position: absolute;
    left: 0;
    font-size: 14px;
    top: 1px;
}

/* Education Styles */
.education-item {
    margin-bottom: 14px;
    padding: 14px 16px;
    border-left: 4px solid #d1d5db;
    background: linear-gradient(135deg, #ffffff, #f9fafb);
    border-radius: 8px;
    page-break-inside: avoid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.education-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #4299e1, transparent);
}

.degree {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.institution {
    font-size: 13px;
    color: #374151;
    display: block;
    margin-bottom: 3px;
}

.year {
    font-size: 12px;
    color: #6b7280;
    font-weight: 600;
    font-style: italic;
}

/* Skills Grid */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
}

.skill-item {
    background: linear-gradient(135deg, #fef5f5, #fed7d7);
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 13px;
    color: #2d3748;
    border-left: 4px solid #fc8181;
    font-weight: 500;
    border: 1px solid #fbb6ce;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.skill-item::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, rgba(229, 62, 62, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.skill-item i {
    color: #e53e3e;
    font-size: 14px;
    width: 24px;
    height: 24px;
    background: rgba(229, 62, 62, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

/* Personal Info */
.personal-info {
    font-size: 13px;
    line-height: 1.6;
    background: linear-gradient(135deg, #ffffff, #f9fafb);
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #4b5563;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.personal-info::before {
    content: '';
    position: absolute;
    top: -30%;
    right: -30%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(66, 153, 225, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.info-row {
    margin-bottom: 10px;
    color: #374151;
}

.info-row strong {
    color: #111827;
    font-weight: 600;
}

/* Optimized Print Styles */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        background: white !important;
        padding: 0;
        margin: 0;
        font-size: 12px;
        line-height: 1.4;
    }

    body::before {
        display: none !important;
    }

    /* Remove decorative elements in print */
    .header::before,
    .summary-text::before,
    .experience-item::before,
    .education-item::before,
    .skill-item::before,
    .personal-info::before,
    .resume-container::before {
        display: none !important;
    }

    .resume-container {
        box-shadow: none;
        max-width: none;
        margin: 0;
        border-radius: 0;
        overflow: visible;
    }

    .page {
        width: 100%;
        height: auto;
        min-height: 10.4in;
        padding: 0;
        margin: 0;
        page-break-after: always;
        page-break-inside: avoid;
        box-shadow: none;
        overflow: visible;
    }

    .page:nth-child(2) {
        padding-top: 20px;
    }

    .page:last-child {
        page-break-after: avoid;
    }



    /* Header */
    .header {
        background: linear-gradient(135deg, #2d3748, #4a5568, #1a202c) !important;
        color: white !important;
        margin-bottom: 6px !important;
        padding: 12px 16px 8px 16px !important;
        box-shadow: none !important;
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
    }

    .header::after {
        height: 2px !important;
        background: linear-gradient(90deg, #fc8181, #f56565, #e53e3e) !important;
    }

    .profile-section {
        flex-direction: column !important;
        align-items: center !important;
        gap: 4px !important;
    }

    .name {
        color: white !important;
        font-size: 20px !important;
        margin-bottom: 1px !important;
    }

    .title {
        color: #fed7aa !important;
        font-size: 11px !important;
        margin-bottom: 6px !important;
    }

    .contact-info {
        flex-direction: row !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 8px !important;
    }

    .contact-item {
        color: white !important;
        font-size: 9px !important;
        gap: 3px !important;
        padding: 2px 4px !important;
    }

    .contact-item i {
        color: #fc8181 !important;
        font-size: 9px !important;
        width: 14px !important;
        height: 14px !important;
    }

    /* Sections */
    .section {
        padding: 0 16px !important;
        margin-bottom: 6px !important;
    }

    .section-title {
        font-size: 12px !important;
        margin-bottom: 5px !important;
        padding-bottom: 3px !important;
        border-bottom: 2px solid #e2e8f0 !important;
        gap: 4px !important;
    }

    .section-title::before {
        display: none !important;
    }

    .section-title::after {
        width: 30px !important;
        height: 2px !important;
        background: linear-gradient(90deg, #fc8181, #f56565) !important;
    }

    .section-title i {
        color: #fc8181 !important;
        font-size: 11px !important;
        width: 20px !important;
        height: 20px !important;
    }

    /* Content */
    .summary-text {
        background: linear-gradient(135deg, #fef5f5, #fed7d7) !important;
        padding: 8px !important;
        border-left: 4px solid #fc8181 !important;
        border-radius: 6px !important;
        font-size: 10px !important;
        line-height: 1.2 !important;
        margin-bottom: 6px !important;
        text-align: justify !important;
        color: #4a5568 !important;
    }

    .experience-item,
    .education-item {
        background: linear-gradient(135deg, #ffffff, #f9fafb) !important;
        border-left: 4px solid #d1d5db !important;
        padding: 6px 8px !important;
        margin-bottom: 5px !important;
        border-radius: 6px !important;
    }

    .job-title,
    .degree {
        font-size: 11px !important;
        margin-bottom: 1px !important;
        color: #111827 !important;
    }

    .company,
    .institution {
        font-size: 9px !important;
        margin-bottom: 0px !important;
        color: #c53030 !important;
    }

    .duration,
    .year {
        font-size: 8px !important;
        color: #6b7280 !important;
    }

    .experience-header {
        margin-bottom: 3px !important;
    }

    .achievements {
        margin-top: 3px !important;
    }

    .achievements li {
        font-size: 9px !important;
        line-height: 1.2 !important;
        margin-bottom: 1px !important;
        padding-left: 12px !important;
        color: #374151 !important;
    }

    .achievements li::before {
        color: #fc8181 !important;
        font-size: 8px !important;
    }

    .skills-grid {
        gap: 4px !important;
    }

    .skill-item {
        background: #fef5f5 !important;
        border: 1px solid #fed7d7 !important;
        border-left: 4px solid #fc8181 !important;
        padding: 3px 6px !important;
        font-size: 9px !important;
        border-radius: 6px !important;
        color: #2d3748 !important;
        display: flex !important;
        align-items: center !important;
        gap: 3px !important;
    }

    .skill-item i {
        color: #e53e3e !important;
        font-size: 9px !important;
        width: 14px !important;
        height: 14px !important;
        text-align: center !important;
        flex-shrink: 0 !important;
    }

    .personal-info {
        background: linear-gradient(135deg, #fefefe, #f8fafc) !important;
        padding: 10px !important;
        border-left: 4px solid #4299e1 !important;
        font-size: 10px !important;
        border-radius: 6px !important;
    }

    .info-row {
        margin-bottom: 6px !important;
        color: #475569 !important;
    }

    .info-row strong {
        color: #1e293b !important;
    }

    @page {
        size: 8.5in 11in;
        margin: 0.15in;
    }

    /* Page breaks */
    .section,
    .experience-item,
    .education-item {
        page-break-inside: avoid;
    }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    body {
        padding: 16px;
    }

    .resume-container {
        max-width: 100%;
        border-radius: 0;
        box-shadow: none;
    }

    .page {
        width: 100%;
        min-height: auto;
        padding: 24px;
    }

    .header {
        flex-direction: column;
        gap: 24px;
        margin-bottom: 32px;
    }

    .contact-info {
        min-width: auto;
        width: 100%;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .name {
        font-size: 28px;
    }

    .profile-section {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
}
